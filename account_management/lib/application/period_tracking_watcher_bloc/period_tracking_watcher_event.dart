part of 'period_tracking_watcher_bloc.dart';

@freezed
class PeriodTrackingWatcherEvent with _$PeriodTrackingWatcherEvent {
  const factory PeriodTrackingWatcherEvent.watchAllStarted() = _WatchAllStarted;
  const factory PeriodTrackingWatcherEvent.watchYearStarted(int year) =
      _WatchYearStarted;
  const factory PeriodTrackingWatcherEvent.dataReceived(
      Either<PeriodTrackingFailure, PeriodTrackingData>
          failureOrPeriodTrackingData) = _DataReceived;
}
