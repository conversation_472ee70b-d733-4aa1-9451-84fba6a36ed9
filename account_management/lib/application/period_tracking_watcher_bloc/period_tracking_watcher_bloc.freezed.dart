// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'period_tracking_watcher_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$PeriodTrackingWatcherEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() watchAllStarted,
    required TResult Function(int year) watchYearStarted,
    required TResult Function(
            Either<PeriodTrackingFailure, PeriodTrackingData>
                failureOrPeriodTrackingData)
        dataReceived,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? watchAllStarted,
    TResult? Function(int year)? watchYearStarted,
    TResult? Function(
            Either<PeriodTrackingFailure, PeriodTrackingData>
                failureOrPeriodTrackingData)?
        dataReceived,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? watchAllStarted,
    TResult Function(int year)? watchYearStarted,
    TResult Function(
            Either<PeriodTrackingFailure, PeriodTrackingData>
                failureOrPeriodTrackingData)?
        dataReceived,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_WatchAllStarted value) watchAllStarted,
    required TResult Function(_WatchYearStarted value) watchYearStarted,
    required TResult Function(_DataReceived value) dataReceived,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_WatchAllStarted value)? watchAllStarted,
    TResult? Function(_WatchYearStarted value)? watchYearStarted,
    TResult? Function(_DataReceived value)? dataReceived,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_WatchAllStarted value)? watchAllStarted,
    TResult Function(_WatchYearStarted value)? watchYearStarted,
    TResult Function(_DataReceived value)? dataReceived,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PeriodTrackingWatcherEventCopyWith<$Res> {
  factory $PeriodTrackingWatcherEventCopyWith(PeriodTrackingWatcherEvent value,
          $Res Function(PeriodTrackingWatcherEvent) then) =
      _$PeriodTrackingWatcherEventCopyWithImpl<$Res,
          PeriodTrackingWatcherEvent>;
}

/// @nodoc
class _$PeriodTrackingWatcherEventCopyWithImpl<$Res,
        $Val extends PeriodTrackingWatcherEvent>
    implements $PeriodTrackingWatcherEventCopyWith<$Res> {
  _$PeriodTrackingWatcherEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PeriodTrackingWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$WatchAllStartedImplCopyWith<$Res> {
  factory _$$WatchAllStartedImplCopyWith(_$WatchAllStartedImpl value,
          $Res Function(_$WatchAllStartedImpl) then) =
      __$$WatchAllStartedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$WatchAllStartedImplCopyWithImpl<$Res>
    extends _$PeriodTrackingWatcherEventCopyWithImpl<$Res,
        _$WatchAllStartedImpl> implements _$$WatchAllStartedImplCopyWith<$Res> {
  __$$WatchAllStartedImplCopyWithImpl(
      _$WatchAllStartedImpl _value, $Res Function(_$WatchAllStartedImpl) _then)
      : super(_value, _then);

  /// Create a copy of PeriodTrackingWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$WatchAllStartedImpl
    with DiagnosticableTreeMixin
    implements _WatchAllStarted {
  const _$WatchAllStartedImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'PeriodTrackingWatcherEvent.watchAllStarted()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties.add(DiagnosticsProperty(
        'type', 'PeriodTrackingWatcherEvent.watchAllStarted'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$WatchAllStartedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() watchAllStarted,
    required TResult Function(int year) watchYearStarted,
    required TResult Function(
            Either<PeriodTrackingFailure, PeriodTrackingData>
                failureOrPeriodTrackingData)
        dataReceived,
  }) {
    return watchAllStarted();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? watchAllStarted,
    TResult? Function(int year)? watchYearStarted,
    TResult? Function(
            Either<PeriodTrackingFailure, PeriodTrackingData>
                failureOrPeriodTrackingData)?
        dataReceived,
  }) {
    return watchAllStarted?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? watchAllStarted,
    TResult Function(int year)? watchYearStarted,
    TResult Function(
            Either<PeriodTrackingFailure, PeriodTrackingData>
                failureOrPeriodTrackingData)?
        dataReceived,
    required TResult orElse(),
  }) {
    if (watchAllStarted != null) {
      return watchAllStarted();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_WatchAllStarted value) watchAllStarted,
    required TResult Function(_WatchYearStarted value) watchYearStarted,
    required TResult Function(_DataReceived value) dataReceived,
  }) {
    return watchAllStarted(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_WatchAllStarted value)? watchAllStarted,
    TResult? Function(_WatchYearStarted value)? watchYearStarted,
    TResult? Function(_DataReceived value)? dataReceived,
  }) {
    return watchAllStarted?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_WatchAllStarted value)? watchAllStarted,
    TResult Function(_WatchYearStarted value)? watchYearStarted,
    TResult Function(_DataReceived value)? dataReceived,
    required TResult orElse(),
  }) {
    if (watchAllStarted != null) {
      return watchAllStarted(this);
    }
    return orElse();
  }
}

abstract class _WatchAllStarted implements PeriodTrackingWatcherEvent {
  const factory _WatchAllStarted() = _$WatchAllStartedImpl;
}

/// @nodoc
abstract class _$$WatchYearStartedImplCopyWith<$Res> {
  factory _$$WatchYearStartedImplCopyWith(_$WatchYearStartedImpl value,
          $Res Function(_$WatchYearStartedImpl) then) =
      __$$WatchYearStartedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({int year});
}

/// @nodoc
class __$$WatchYearStartedImplCopyWithImpl<$Res>
    extends _$PeriodTrackingWatcherEventCopyWithImpl<$Res,
        _$WatchYearStartedImpl>
    implements _$$WatchYearStartedImplCopyWith<$Res> {
  __$$WatchYearStartedImplCopyWithImpl(_$WatchYearStartedImpl _value,
      $Res Function(_$WatchYearStartedImpl) _then)
      : super(_value, _then);

  /// Create a copy of PeriodTrackingWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? year = null,
  }) {
    return _then(_$WatchYearStartedImpl(
      null == year
          ? _value.year
          : year // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$WatchYearStartedImpl
    with DiagnosticableTreeMixin
    implements _WatchYearStarted {
  const _$WatchYearStartedImpl(this.year);

  @override
  final int year;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'PeriodTrackingWatcherEvent.watchYearStarted(year: $year)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty(
          'type', 'PeriodTrackingWatcherEvent.watchYearStarted'))
      ..add(DiagnosticsProperty('year', year));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WatchYearStartedImpl &&
            (identical(other.year, year) || other.year == year));
  }

  @override
  int get hashCode => Object.hash(runtimeType, year);

  /// Create a copy of PeriodTrackingWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$WatchYearStartedImplCopyWith<_$WatchYearStartedImpl> get copyWith =>
      __$$WatchYearStartedImplCopyWithImpl<_$WatchYearStartedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() watchAllStarted,
    required TResult Function(int year) watchYearStarted,
    required TResult Function(
            Either<PeriodTrackingFailure, PeriodTrackingData>
                failureOrPeriodTrackingData)
        dataReceived,
  }) {
    return watchYearStarted(year);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? watchAllStarted,
    TResult? Function(int year)? watchYearStarted,
    TResult? Function(
            Either<PeriodTrackingFailure, PeriodTrackingData>
                failureOrPeriodTrackingData)?
        dataReceived,
  }) {
    return watchYearStarted?.call(year);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? watchAllStarted,
    TResult Function(int year)? watchYearStarted,
    TResult Function(
            Either<PeriodTrackingFailure, PeriodTrackingData>
                failureOrPeriodTrackingData)?
        dataReceived,
    required TResult orElse(),
  }) {
    if (watchYearStarted != null) {
      return watchYearStarted(year);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_WatchAllStarted value) watchAllStarted,
    required TResult Function(_WatchYearStarted value) watchYearStarted,
    required TResult Function(_DataReceived value) dataReceived,
  }) {
    return watchYearStarted(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_WatchAllStarted value)? watchAllStarted,
    TResult? Function(_WatchYearStarted value)? watchYearStarted,
    TResult? Function(_DataReceived value)? dataReceived,
  }) {
    return watchYearStarted?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_WatchAllStarted value)? watchAllStarted,
    TResult Function(_WatchYearStarted value)? watchYearStarted,
    TResult Function(_DataReceived value)? dataReceived,
    required TResult orElse(),
  }) {
    if (watchYearStarted != null) {
      return watchYearStarted(this);
    }
    return orElse();
  }
}

abstract class _WatchYearStarted implements PeriodTrackingWatcherEvent {
  const factory _WatchYearStarted(final int year) = _$WatchYearStartedImpl;

  int get year;

  /// Create a copy of PeriodTrackingWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$WatchYearStartedImplCopyWith<_$WatchYearStartedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DataReceivedImplCopyWith<$Res> {
  factory _$$DataReceivedImplCopyWith(
          _$DataReceivedImpl value, $Res Function(_$DataReceivedImpl) then) =
      __$$DataReceivedImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {Either<PeriodTrackingFailure, PeriodTrackingData>
          failureOrPeriodTrackingData});
}

/// @nodoc
class __$$DataReceivedImplCopyWithImpl<$Res>
    extends _$PeriodTrackingWatcherEventCopyWithImpl<$Res, _$DataReceivedImpl>
    implements _$$DataReceivedImplCopyWith<$Res> {
  __$$DataReceivedImplCopyWithImpl(
      _$DataReceivedImpl _value, $Res Function(_$DataReceivedImpl) _then)
      : super(_value, _then);

  /// Create a copy of PeriodTrackingWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failureOrPeriodTrackingData = null,
  }) {
    return _then(_$DataReceivedImpl(
      null == failureOrPeriodTrackingData
          ? _value.failureOrPeriodTrackingData
          : failureOrPeriodTrackingData // ignore: cast_nullable_to_non_nullable
              as Either<PeriodTrackingFailure, PeriodTrackingData>,
    ));
  }
}

/// @nodoc

class _$DataReceivedImpl with DiagnosticableTreeMixin implements _DataReceived {
  const _$DataReceivedImpl(this.failureOrPeriodTrackingData);

  @override
  final Either<PeriodTrackingFailure, PeriodTrackingData>
      failureOrPeriodTrackingData;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'PeriodTrackingWatcherEvent.dataReceived(failureOrPeriodTrackingData: $failureOrPeriodTrackingData)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty(
          'type', 'PeriodTrackingWatcherEvent.dataReceived'))
      ..add(DiagnosticsProperty(
          'failureOrPeriodTrackingData', failureOrPeriodTrackingData));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DataReceivedImpl &&
            (identical(other.failureOrPeriodTrackingData,
                    failureOrPeriodTrackingData) ||
                other.failureOrPeriodTrackingData ==
                    failureOrPeriodTrackingData));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failureOrPeriodTrackingData);

  /// Create a copy of PeriodTrackingWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DataReceivedImplCopyWith<_$DataReceivedImpl> get copyWith =>
      __$$DataReceivedImplCopyWithImpl<_$DataReceivedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() watchAllStarted,
    required TResult Function(int year) watchYearStarted,
    required TResult Function(
            Either<PeriodTrackingFailure, PeriodTrackingData>
                failureOrPeriodTrackingData)
        dataReceived,
  }) {
    return dataReceived(failureOrPeriodTrackingData);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? watchAllStarted,
    TResult? Function(int year)? watchYearStarted,
    TResult? Function(
            Either<PeriodTrackingFailure, PeriodTrackingData>
                failureOrPeriodTrackingData)?
        dataReceived,
  }) {
    return dataReceived?.call(failureOrPeriodTrackingData);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? watchAllStarted,
    TResult Function(int year)? watchYearStarted,
    TResult Function(
            Either<PeriodTrackingFailure, PeriodTrackingData>
                failureOrPeriodTrackingData)?
        dataReceived,
    required TResult orElse(),
  }) {
    if (dataReceived != null) {
      return dataReceived(failureOrPeriodTrackingData);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_WatchAllStarted value) watchAllStarted,
    required TResult Function(_WatchYearStarted value) watchYearStarted,
    required TResult Function(_DataReceived value) dataReceived,
  }) {
    return dataReceived(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_WatchAllStarted value)? watchAllStarted,
    TResult? Function(_WatchYearStarted value)? watchYearStarted,
    TResult? Function(_DataReceived value)? dataReceived,
  }) {
    return dataReceived?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_WatchAllStarted value)? watchAllStarted,
    TResult Function(_WatchYearStarted value)? watchYearStarted,
    TResult Function(_DataReceived value)? dataReceived,
    required TResult orElse(),
  }) {
    if (dataReceived != null) {
      return dataReceived(this);
    }
    return orElse();
  }
}

abstract class _DataReceived implements PeriodTrackingWatcherEvent {
  const factory _DataReceived(
      final Either<PeriodTrackingFailure, PeriodTrackingData>
          failureOrPeriodTrackingData) = _$DataReceivedImpl;

  Either<PeriodTrackingFailure, PeriodTrackingData>
      get failureOrPeriodTrackingData;

  /// Create a copy of PeriodTrackingWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DataReceivedImplCopyWith<_$DataReceivedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$PeriodTrackingWatcherState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loading,
    required TResult Function(
            List<PeriodTrackingModel> periodTrackingDetails,
            DateTime focusedDay,
            Set<DateTime> selectedDays,
            Set<DateTime> ovulationDays,
            PeriodTrackingModel selectedPeriodTrackingDay,
            int updateCounter)
        data,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loading,
    TResult? Function(
            List<PeriodTrackingModel> periodTrackingDetails,
            DateTime focusedDay,
            Set<DateTime> selectedDays,
            Set<DateTime> ovulationDays,
            PeriodTrackingModel selectedPeriodTrackingDay,
            int updateCounter)?
        data,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loading,
    TResult Function(
            List<PeriodTrackingModel> periodTrackingDetails,
            DateTime focusedDay,
            Set<DateTime> selectedDays,
            Set<DateTime> ovulationDays,
            PeriodTrackingModel selectedPeriodTrackingDay,
            int updateCounter)?
        data,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Loading value) loading,
    required TResult Function(_Data value) data,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Data value)? data,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Loading value)? loading,
    TResult Function(_Data value)? data,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PeriodTrackingWatcherStateCopyWith<$Res> {
  factory $PeriodTrackingWatcherStateCopyWith(PeriodTrackingWatcherState value,
          $Res Function(PeriodTrackingWatcherState) then) =
      _$PeriodTrackingWatcherStateCopyWithImpl<$Res,
          PeriodTrackingWatcherState>;
}

/// @nodoc
class _$PeriodTrackingWatcherStateCopyWithImpl<$Res,
        $Val extends PeriodTrackingWatcherState>
    implements $PeriodTrackingWatcherStateCopyWith<$Res> {
  _$PeriodTrackingWatcherStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PeriodTrackingWatcherState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$LoadingImplCopyWith<$Res> {
  factory _$$LoadingImplCopyWith(
          _$LoadingImpl value, $Res Function(_$LoadingImpl) then) =
      __$$LoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadingImplCopyWithImpl<$Res>
    extends _$PeriodTrackingWatcherStateCopyWithImpl<$Res, _$LoadingImpl>
    implements _$$LoadingImplCopyWith<$Res> {
  __$$LoadingImplCopyWithImpl(
      _$LoadingImpl _value, $Res Function(_$LoadingImpl) _then)
      : super(_value, _then);

  /// Create a copy of PeriodTrackingWatcherState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadingImpl extends _Loading with DiagnosticableTreeMixin {
  const _$LoadingImpl() : super._();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'PeriodTrackingWatcherState.loading()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
        .add(DiagnosticsProperty('type', 'PeriodTrackingWatcherState.loading'));
  }

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loading,
    required TResult Function(
            List<PeriodTrackingModel> periodTrackingDetails,
            DateTime focusedDay,
            Set<DateTime> selectedDays,
            Set<DateTime> ovulationDays,
            PeriodTrackingModel selectedPeriodTrackingDay,
            int updateCounter)
        data,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loading,
    TResult? Function(
            List<PeriodTrackingModel> periodTrackingDetails,
            DateTime focusedDay,
            Set<DateTime> selectedDays,
            Set<DateTime> ovulationDays,
            PeriodTrackingModel selectedPeriodTrackingDay,
            int updateCounter)?
        data,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loading,
    TResult Function(
            List<PeriodTrackingModel> periodTrackingDetails,
            DateTime focusedDay,
            Set<DateTime> selectedDays,
            Set<DateTime> ovulationDays,
            PeriodTrackingModel selectedPeriodTrackingDay,
            int updateCounter)?
        data,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Loading value) loading,
    required TResult Function(_Data value) data,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Data value)? data,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Loading value)? loading,
    TResult Function(_Data value)? data,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class _Loading extends PeriodTrackingWatcherState {
  const factory _Loading() = _$LoadingImpl;
  const _Loading._() : super._();
}

/// @nodoc
abstract class _$$DataImplCopyWith<$Res> {
  factory _$$DataImplCopyWith(
          _$DataImpl value, $Res Function(_$DataImpl) then) =
      __$$DataImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {List<PeriodTrackingModel> periodTrackingDetails,
      DateTime focusedDay,
      Set<DateTime> selectedDays,
      Set<DateTime> ovulationDays,
      PeriodTrackingModel selectedPeriodTrackingDay,
      int updateCounter});
}

/// @nodoc
class __$$DataImplCopyWithImpl<$Res>
    extends _$PeriodTrackingWatcherStateCopyWithImpl<$Res, _$DataImpl>
    implements _$$DataImplCopyWith<$Res> {
  __$$DataImplCopyWithImpl(_$DataImpl _value, $Res Function(_$DataImpl) _then)
      : super(_value, _then);

  /// Create a copy of PeriodTrackingWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? periodTrackingDetails = null,
    Object? focusedDay = null,
    Object? selectedDays = null,
    Object? ovulationDays = null,
    Object? selectedPeriodTrackingDay = null,
    Object? updateCounter = null,
  }) {
    return _then(_$DataImpl(
      periodTrackingDetails: null == periodTrackingDetails
          ? _value._periodTrackingDetails
          : periodTrackingDetails // ignore: cast_nullable_to_non_nullable
              as List<PeriodTrackingModel>,
      focusedDay: null == focusedDay
          ? _value.focusedDay
          : focusedDay // ignore: cast_nullable_to_non_nullable
              as DateTime,
      selectedDays: null == selectedDays
          ? _value._selectedDays
          : selectedDays // ignore: cast_nullable_to_non_nullable
              as Set<DateTime>,
      ovulationDays: null == ovulationDays
          ? _value._ovulationDays
          : ovulationDays // ignore: cast_nullable_to_non_nullable
              as Set<DateTime>,
      selectedPeriodTrackingDay: null == selectedPeriodTrackingDay
          ? _value.selectedPeriodTrackingDay
          : selectedPeriodTrackingDay // ignore: cast_nullable_to_non_nullable
              as PeriodTrackingModel,
      updateCounter: null == updateCounter
          ? _value.updateCounter
          : updateCounter // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$DataImpl extends _Data with DiagnosticableTreeMixin {
  const _$DataImpl(
      {required final List<PeriodTrackingModel> periodTrackingDetails,
      required this.focusedDay,
      required final Set<DateTime> selectedDays,
      required final Set<DateTime> ovulationDays,
      required this.selectedPeriodTrackingDay,
      required this.updateCounter})
      : _periodTrackingDetails = periodTrackingDetails,
        _selectedDays = selectedDays,
        _ovulationDays = ovulationDays,
        super._();

  final List<PeriodTrackingModel> _periodTrackingDetails;
  @override
  List<PeriodTrackingModel> get periodTrackingDetails {
    if (_periodTrackingDetails is EqualUnmodifiableListView)
      return _periodTrackingDetails;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_periodTrackingDetails);
  }

  @override
  final DateTime focusedDay;
  final Set<DateTime> _selectedDays;
  @override
  Set<DateTime> get selectedDays {
    if (_selectedDays is EqualUnmodifiableSetView) return _selectedDays;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableSetView(_selectedDays);
  }

  final Set<DateTime> _ovulationDays;
  @override
  Set<DateTime> get ovulationDays {
    if (_ovulationDays is EqualUnmodifiableSetView) return _ovulationDays;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableSetView(_ovulationDays);
  }

  @override
  final PeriodTrackingModel selectedPeriodTrackingDay;
  @override
  final int updateCounter;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'PeriodTrackingWatcherState.data(periodTrackingDetails: $periodTrackingDetails, focusedDay: $focusedDay, selectedDays: $selectedDays, ovulationDays: $ovulationDays, selectedPeriodTrackingDay: $selectedPeriodTrackingDay, updateCounter: $updateCounter)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'PeriodTrackingWatcherState.data'))
      ..add(DiagnosticsProperty('periodTrackingDetails', periodTrackingDetails))
      ..add(DiagnosticsProperty('focusedDay', focusedDay))
      ..add(DiagnosticsProperty('selectedDays', selectedDays))
      ..add(DiagnosticsProperty('ovulationDays', ovulationDays))
      ..add(DiagnosticsProperty(
          'selectedPeriodTrackingDay', selectedPeriodTrackingDay))
      ..add(DiagnosticsProperty('updateCounter', updateCounter));
  }

  /// Create a copy of PeriodTrackingWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DataImplCopyWith<_$DataImpl> get copyWith =>
      __$$DataImplCopyWithImpl<_$DataImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loading,
    required TResult Function(
            List<PeriodTrackingModel> periodTrackingDetails,
            DateTime focusedDay,
            Set<DateTime> selectedDays,
            Set<DateTime> ovulationDays,
            PeriodTrackingModel selectedPeriodTrackingDay,
            int updateCounter)
        data,
  }) {
    return data(periodTrackingDetails, focusedDay, selectedDays, ovulationDays,
        selectedPeriodTrackingDay, updateCounter);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loading,
    TResult? Function(
            List<PeriodTrackingModel> periodTrackingDetails,
            DateTime focusedDay,
            Set<DateTime> selectedDays,
            Set<DateTime> ovulationDays,
            PeriodTrackingModel selectedPeriodTrackingDay,
            int updateCounter)?
        data,
  }) {
    return data?.call(periodTrackingDetails, focusedDay, selectedDays,
        ovulationDays, selectedPeriodTrackingDay, updateCounter);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loading,
    TResult Function(
            List<PeriodTrackingModel> periodTrackingDetails,
            DateTime focusedDay,
            Set<DateTime> selectedDays,
            Set<DateTime> ovulationDays,
            PeriodTrackingModel selectedPeriodTrackingDay,
            int updateCounter)?
        data,
    required TResult orElse(),
  }) {
    if (data != null) {
      return data(periodTrackingDetails, focusedDay, selectedDays,
          ovulationDays, selectedPeriodTrackingDay, updateCounter);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Loading value) loading,
    required TResult Function(_Data value) data,
  }) {
    return data(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Data value)? data,
  }) {
    return data?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Loading value)? loading,
    TResult Function(_Data value)? data,
    required TResult orElse(),
  }) {
    if (data != null) {
      return data(this);
    }
    return orElse();
  }
}

abstract class _Data extends PeriodTrackingWatcherState {
  const factory _Data(
      {required final List<PeriodTrackingModel> periodTrackingDetails,
      required final DateTime focusedDay,
      required final Set<DateTime> selectedDays,
      required final Set<DateTime> ovulationDays,
      required final PeriodTrackingModel selectedPeriodTrackingDay,
      required final int updateCounter}) = _$DataImpl;
  const _Data._() : super._();

  List<PeriodTrackingModel> get periodTrackingDetails;
  DateTime get focusedDay;
  Set<DateTime> get selectedDays;
  Set<DateTime> get ovulationDays;
  PeriodTrackingModel get selectedPeriodTrackingDay;
  int get updateCounter;

  /// Create a copy of PeriodTrackingWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DataImplCopyWith<_$DataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
