import 'dart:async';

import 'package:account_management/account_management.dart';
import 'package:account_management/domain/model/health_data.dart';
import 'package:account_management/repository/menstrual_cycle_data_model.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:fpdart/fpdart.dart';
import 'package:injectable/injectable.dart';
import 'package:rxdart/rxdart.dart';
import '../domain/facade/period_tracking_facade.dart';
import '../domain/failure/period_tracking_failure.dart';

@LazySingleton(as: PeriodTrackingFacade)
class PeriodTrackingRepository implements PeriodTrackingFacade {

}
