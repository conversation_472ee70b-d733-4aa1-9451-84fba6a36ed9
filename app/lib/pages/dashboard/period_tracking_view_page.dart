import 'package:account_management/application/period_tracking_watcher_bloc/period_tracking_watcher_bloc.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:juno_plus/routing/app_pages.gr.dart';
import '../../custom_widgets/dotted_border_widget.dart';
import '../../helpers.dart';
import 'package:intl/intl.dart';

@RoutePage()
class PeriodTrackingViewPage extends StatelessWidget {
  const PeriodTrackingViewPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider<PeriodTrackingWatcherBloc>(
      create: (context) => getIt<PeriodTrackingWatcherBloc>()
        ..add(PeriodTrackingWatcherEvent.watchYearStarted(DateTime.now().year)),
      child: PeriodTrackingViewScaffold(),
    );
  }
}

class PeriodTrackingViewScaffold extends StatefulWidget {
  const PeriodTrackingViewScaffold({super.key});

  @override
  State<PeriodTrackingViewScaffold> createState() =>
      _PeriodTrackingViewScaffoldState();
}

// Optimized date state cache for performance
class DateState {
  final bool isTodayDate;
  final bool isFutureDate;
  final bool isSelectedPeriodDate;
  final bool isFirstPeriodDate;
  final bool isLastPeriodDate;
  final bool isInPeriodWindow;
  final bool isOvulationDate;
  final bool isFirstOvulationDate;
  final bool isLastOvulationDate;
  final bool isInOvulationWindow;

  const DateState({
    this.isTodayDate = false,
    this.isFutureDate = false,
    this.isSelectedPeriodDate = false,
    this.isFirstPeriodDate = false,
    this.isLastPeriodDate = false,
    this.isInPeriodWindow = false,
    this.isOvulationDate = false,
    this.isFirstOvulationDate = false,
    this.isLastOvulationDate = false,
    this.isInOvulationWindow = false,
  });
}

class _PeriodTrackingViewScaffoldState
    extends State<PeriodTrackingViewScaffold> {
  // Cache for optimized performance
  Map<DateTime, DateState> _dateStateCache = {};
  List<DateTime> _months = [];
  int _selectedYear = DateTime.now().year;

  @override
  void initState() {
    super.initState();
    _months = _getMonthsToDisplay(_selectedYear);
  }

  // Generate list of months to display for a specific year
  List<DateTime> _getMonthsToDisplay(int year) {
    List<DateTime> months = [];

    // Add all 12 months of the selected year
    for (int month = 1; month <= 12; month++) {
      months.add(DateTime(year, month, 1));
    }

    return months;
  }

  // Get available years (current year and past 5 years)
  List<int> _getAvailableYears() {
    final currentYear = DateTime.now().year;
    List<int> years = [];

    for (int i = 0; i < 6; i++) {
      years.add(currentYear - i);
    }

    return years;
  }

  // Pre-calculate all date states for performance optimization
  void _calculateDateStates(
      Set<DateTime> periodDates, Set<DateTime> ovulationDates) {
    _dateStateCache.clear();
    final today = DateTime.now();
    final todayNormalized = DateTime(today.year, today.month, today.day);

    // Pre-calculate period cycles for efficiency
    final periodCycles = _calculatePeriodCycles(periodDates);
    final ovulationCycles = _calculateOvulationCycles(ovulationDates);

    for (final month in _months) {
      final daysInMonth = DateTime(month.year, month.month + 1, 0).day;

      for (int day = 1; day <= daysInMonth; day++) {
        final date = DateTime(month.year, month.month, day);
        final dateNormalized = DateTime(date.year, date.month, date.day);

        _dateStateCache[dateNormalized] = DateState(
          isTodayDate: dateNormalized.isAtSameMomentAs(todayNormalized),
          isFutureDate: dateNormalized.isAfter(todayNormalized),
          isSelectedPeriodDate: periodDates.any((d) => _isSameDate(d, date)),
          isFirstPeriodDate: _isFirstInCycles(date, periodCycles),
          isLastPeriodDate: _isLastInCycles(date, periodCycles),
          isInPeriodWindow: _isInPeriodWindow(date, periodCycles),
          isOvulationDate: ovulationDates.any((d) => _isSameDate(d, date)),
          isFirstOvulationDate: _isFirstInCycles(date, ovulationCycles),
          isLastOvulationDate: _isLastInCycles(date, ovulationCycles),
          isInOvulationWindow: _isInOvulationWindow(date, ovulationDates),
        );
      }
    }
  }

  bool _isSameDate(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  List<List<DateTime>> _calculatePeriodCycles(Set<DateTime> periodDates) {
    if (periodDates.isEmpty) return [];

    List<DateTime> sortedPeriodDates = periodDates.toList()..sort();
    List<List<DateTime>> periodCycles = [];
    List<DateTime> currentCycle = [];

    for (int i = 0; i < sortedPeriodDates.length; i++) {
      if (currentCycle.isEmpty) {
        currentCycle.add(sortedPeriodDates[i]);
      } else {
        DateTime lastDate = currentCycle.last;
        int daysDifference = sortedPeriodDates[i].difference(lastDate).inDays;

        if (daysDifference <= 1) {
          currentCycle.add(sortedPeriodDates[i]);
        } else {
          periodCycles.add(List.from(currentCycle));
          currentCycle = [sortedPeriodDates[i]];
        }
      }
    }
    if (currentCycle.isNotEmpty) {
      periodCycles.add(currentCycle);
    }

    return periodCycles;
  }

  List<List<DateTime>> _calculateOvulationCycles(Set<DateTime> ovulationDates) {
    if (ovulationDates.isEmpty) return [];

    List<DateTime> sortedOvulationDates = ovulationDates.toList()..sort();
    List<List<DateTime>> ovulationCycles = [];
    List<DateTime> currentCycle = [];

    for (int i = 0; i < sortedOvulationDates.length; i++) {
      if (currentCycle.isEmpty) {
        currentCycle.add(sortedOvulationDates[i]);
      } else {
        DateTime lastDate = currentCycle.last;
        int daysDifference =
            sortedOvulationDates[i].difference(lastDate).inDays;

        if (daysDifference <= 1) {
          currentCycle.add(sortedOvulationDates[i]);
        } else {
          ovulationCycles.add(List.from(currentCycle));
          currentCycle = [sortedOvulationDates[i]];
        }
      }
    }
    if (currentCycle.isNotEmpty) {
      ovulationCycles.add(currentCycle);
    }

    return ovulationCycles;
  }

  bool _isFirstInCycles(DateTime date, List<List<DateTime>> cycles) {
    for (List<DateTime> cycle in cycles) {
      DateTime firstDate = cycle.first;
      if (_isSameDate(firstDate, date)) {
        return true;
      }
    }
    return false;
  }

  bool _isLastInCycles(DateTime date, List<List<DateTime>> cycles) {
    for (List<DateTime> cycle in cycles) {
      DateTime lastDate = cycle.last;
      if (_isSameDate(lastDate, date)) {
        return true;
      }
    }
    return false;
  }

  bool _isInPeriodWindow(DateTime date, List<List<DateTime>> periodCycles) {
    for (List<DateTime> cycle in periodCycles) {
      if (cycle.length >= 3) {
        DateTime startDate = cycle.first;
        DateTime endDate = cycle.last;

        if (date.isAfter(startDate) &&
            date.isBefore(endDate) &&
            !cycle.any((periodDate) => _isSameDate(periodDate, date))) {
          return true;
        }
      }
    }
    return false;
  }

  bool _isInOvulationWindow(DateTime date, Set<DateTime> ovulationDates) {
    for (DateTime ovulationDate in ovulationDates) {
      final difference = date.difference(ovulationDate).inDays.abs();
      if (difference <= 2 && difference > 0) {
        return true;
      }
    }
    return false;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xffFAF2DF),
      appBar: AppBar(
        title: Text('Period Insights'),
        backgroundColor: Color(0xffFAF2DF),
        elevation: 0,
        actions: [
          // Year dropdown
          Container(
            margin: EdgeInsets.only(right: 8.w),
            padding: EdgeInsets.symmetric(horizontal: 12.w),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: DropdownButton<int>(
              value: _selectedYear,
              underline: SizedBox(),
              items: _getAvailableYears().map((year) {
                return DropdownMenuItem<int>(
                  value: year,
                  child: Text(year.toString()),
                );
              }).toList(),
              onChanged: (newYear) {
                if (newYear != null && newYear != _selectedYear) {
                  setState(() {
                    _selectedYear = newYear;
                    _months = _getMonthsToDisplay(_selectedYear);
                  });
                  // Update the bloc to watch the new year
                  context.read<PeriodTrackingWatcherBloc>().add(
                        PeriodTrackingWatcherEvent.watchYearStarted(newYear),
                      );
                }
              },
            ),
          ),
          IconButton(
            icon: Icon(Icons.edit),
            onPressed: () {
              // Navigate to edit page
              context.router.push(PeriodTrackingEditRoute());
            },
          ),
        ],
      ),
      body: BlocBuilder<PeriodTrackingWatcherBloc, PeriodTrackingWatcherState>(
        builder: (context, state) {
          return state.maybeMap(
            data: (dataState) {
              // Pre-calculate date states when data changes
              _calculateDateStates(
                  dataState.selectedDays, dataState.ovulationDays);

              return ListView.builder(
                itemCount: _months.length,
                itemBuilder: (context, index) {
                  return _buildOptimizedMonthCalendar(_months[index]);
                },
              );
            },
            orElse: () => Center(child: CircularProgressIndicator()),
          );
        },
      ),
    );
  }

  // Optimized month calendar builder using simple widgets
  Widget _buildOptimizedMonthCalendar(DateTime month) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      child: Container(
        margin: EdgeInsets.symmetric(vertical: 8.h, horizontal: 16.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.2),
              spreadRadius: 2,
              blurRadius: 5,
              offset: Offset(0, 3),
            ),
          ],
        ),
        child: Padding(
          padding:
              EdgeInsets.only(top: 16.h, left: 16.w, right: 16.w, bottom: 20.h),
          child: Column(
            children: [
              // Month header
              Text(
                DateFormat('MMMM yyyy').format(month),
                style: GoogleFonts.roboto(
                  fontSize: 30.sp,
                  fontWeight: FontWeight.w400,
                  color: Color(0xff6C618B),
                ),
              ),
              SizedBox(height: 16.h),
              // Days of week header
              _buildDaysOfWeekHeader(),
              SizedBox(height: 8.h),
              // Calendar grid
              _buildCalendarGrid(month),
            ],
          ),
        ),
      ),
    );
  }

  // Days of week header
  Widget _buildDaysOfWeekHeader() {
    final daysOfWeek = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    return Row(
      children: daysOfWeek
          .map((day) => Expanded(
                child: Center(
                  child: Text(
                    day,
                    style: GoogleFonts.roboto(
                      fontSize: 25.sp,
                      fontWeight: FontWeight.w500,
                      color: Color(0xff71456F),
                    ),
                  ),
                ),
              ))
          .toList(),
    );
  }

  // Optimized calendar grid using simple Row/Column widgets
  Widget _buildCalendarGrid(DateTime month) {
    final firstDayOfMonth = DateTime(month.year, month.month, 1);
    final lastDayOfMonth = DateTime(month.year, month.month + 1, 0);
    final daysInMonth = lastDayOfMonth.day;
    final firstWeekday = firstDayOfMonth.weekday % 7; // Sunday = 0

    List<Widget> weeks = [];
    List<Widget> currentWeek = [];

    // Add empty cells for days before the first day of the month
    for (int i = 0; i < firstWeekday; i++) {
      currentWeek.add(Expanded(child: Container()));
    }

    // Add days of the month
    for (int day = 1; day <= daysInMonth; day++) {
      final date = DateTime(month.year, month.month, day);
      currentWeek.add(Expanded(child: _buildOptimizedDayCell(date)));

      // If we've filled a week (7 days), start a new week
      if (currentWeek.length == 7) {
        weeks.add(Row(children: List.from(currentWeek)));
        currentWeek.clear();
      }
    }

    // Fill remaining cells in the last week
    while (currentWeek.length < 7) {
      currentWeek.add(Expanded(child: Container()));
    }
    if (currentWeek.isNotEmpty) {
      weeks.add(Row(children: List.from(currentWeek)));
    }

    return Column(children: weeks);
  }

  // Optimized day cell builder using cached states
  Widget _buildOptimizedDayCell(DateTime date) {
    final dateNormalized = DateTime(date.year, date.month, date.day);
    final dateState = _dateStateCache[dateNormalized] ?? DateState();

    // Build the appropriate widget using the same logic as original but optimized
    return Container(
      height: 55.h,
      child: _buildDayWidget(
        date: date,
        dateState: dateState,
      ),
    );
  }

  // Main widget builder that uses cached date states for performance
  Widget _buildDayWidget({
    required DateTime date,
    required DateState dateState,
  }) {
    // Priority order: Today > Period dates > Ovulation dates > Windows > Regular
    if (dateState.isTodayDate) {
      return _buildTodayDate(date);
    }

    // Period dates (first/last have priority over middle)
    if (dateState.isFirstPeriodDate) {
      return _buildPeriodFirstDate(date, dateState.isFutureDate);
    }
    if (dateState.isLastPeriodDate) {
      return _buildPeriodLastDate(date, dateState.isFutureDate);
    }
    if (dateState.isSelectedPeriodDate) {
      return _buildPeriodMiddleDate(date, dateState.isFutureDate);
    }

    // Ovulation dates (first/last have priority over middle)
    if (dateState.isFirstOvulationDate) {
      return _buildOvulationFirstDate(date, dateState.isFutureDate);
    }
    if (dateState.isLastOvulationDate) {
      return _buildOvulationLastDate(date, dateState.isFutureDate);
    }
    if (dateState.isOvulationDate) {
      return _buildOvulationMiddleDate(date, dateState.isFutureDate);
    }

    // Regular date
    return _buildRegularDate(date);
  }

  // Identical styling methods from original implementation
  Widget _buildTodayDate(DateTime date) {
    return Container(
      width: 55.w,
      height: 55.h,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(color: Color(0xff5DADE2), width: 2),
      ),
      child: Center(
        child: Text(
          '${date.day}',
          style: GoogleFonts.roboto(
            color: Color(0xff5DADE2),
            fontWeight: FontWeight.w500,
            fontSize: 25.sp,
          ),
        ),
      ),
    );
  }

  Widget _buildPeriodFirstDate(DateTime date, bool isFuture) {
    return Container(
      width: 100.w,
      height: 55.h,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          // Light purple extension to the right
          Positioned(
            left: 15.0.w,
            top: 0,
            child: Container(
              width: 100.w,
              height: 55.h,
              decoration: BoxDecoration(
                color: isFuture ? Color(0xffE4DEFF) : Color(0xffCFB4FE),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(27.5.h),
                  bottomLeft: Radius.circular(27.5.h),
                ),
              ),
            ),
          ),
          // Circle centered
          Center(
            child: isFuture
                ? DottedBorder(
                    color: Color(0xff584294),
                    strokeWidth: 1.5,
                    child: _buildRegularDate(date),
                  )
                : _buildCircleContent(date, Color(0xff584294), Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildPeriodLastDate(DateTime date, bool isFuture) {
    return Container(
      width: 100.w,
      height: 55.h,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          // Light purple extension to the left
          Positioned(
            right: 15.w,
            top: 0,
            child: Container(
              width: 100.w,
              height: 55.h,
              decoration: BoxDecoration(
                color: isFuture ? Color(0xffE4DEFF) : Color(0xffCFB4FE),
                borderRadius: BorderRadius.only(
                  topRight: Radius.circular(27.5.h),
                  bottomRight: Radius.circular(27.5.h),
                ),
              ),
            ),
          ),
          // Circle centered
          Center(
            child: isFuture
                ? DottedBorder(
                    color: Color(0xff584294),
                    strokeWidth: 1.5,
                    child: _buildRegularDate(date),
                  )
                : _buildCircleContent(date, Color(0xff584294), Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildPeriodMiddleDate(DateTime date, bool isFuture) {
    return Container(
      width: 104.w,
      height: 55.h,
      decoration: BoxDecoration(
        color: isFuture ? Color(0xffE4DEFF) : Color(0xffCFB4FE),
      ),
      child: Center(
        child: Text(
          '${date.day}',
          style: GoogleFonts.roboto(
            color: isFuture ? Color(0xff71456F) : Colors.white,
            fontWeight: FontWeight.w500,
            fontSize: 25.sp,
          ),
        ),
      ),
    );
  }

  Widget _buildOvulationFirstDate(DateTime date, bool isFuture) {
    return Container(
      width: 100.w,
      height: 55.h,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          // Light purple extension to the right
          Positioned(
            left: 15.0.w,
            top: 0,
            child: Container(
              width: 100.w,
              height: 55.h,
              decoration: BoxDecoration(
                color: isFuture ? Color(0xffFFECC7) : Color(0xffFFDA94),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(27.5.h),
                  bottomLeft: Radius.circular(27.5.h),
                ),
              ),
            ),
          ),
          // Circle centered
          Center(
            child: _buildRegularDate(date),
          ),
        ],
      ),
    );
  }

  Widget _buildOvulationLastDate(DateTime date, bool isFuture) {
    return Container(
      width: 100.w,
      height: 55.h,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          // Light purple extension to the right
          Positioned(
            right: 15.0.w,
            top: 0,
            child: Container(
              width: 100.w,
              height: 55.h,
              decoration: BoxDecoration(
                color: isFuture ? Color(0xffFFECC7) : Color(0xffFFDA94),
                borderRadius: BorderRadius.only(
                  topRight: Radius.circular(27.5.h),
                  bottomRight: Radius.circular(27.5.h),
                ),
              ),
            ),
          ),
          // Circle centered
          Center(
            child: _buildRegularDate(date),
          ),
        ],
      ),
    );
  }

  Widget _buildOvulationMiddleDate(DateTime date, bool isFuture) {
    return Container(
      width: 104.w,
      height: 55.h,
      decoration: BoxDecoration(
        color: isFuture ? Color(0xffFFECC7) : Color(0xffFFDA94),
      ),
      child: Center(
        child: Text(
          '${date.day}',
          style: GoogleFonts.roboto(
            color: Color(0xff71456F),
            fontWeight: FontWeight.w500,
            fontSize: 25.sp,
          ),
        ),
      ),
    );
  }

  Widget _buildCircleContent(
      DateTime date, Color backgroundColor, Color textColor) {
    return Container(
      width: 60.w,
      height: 60.h,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: backgroundColor,
      ),
      child: Center(
        child: Text(
          '${date.day}',
          style: GoogleFonts.roboto(
            color: textColor,
            fontWeight: FontWeight.w500,
            fontSize: 25.sp,
          ),
        ),
      ),
    );
  }

  Widget _buildRegularDate(DateTime date) {
    return Container(
      width: 55.w,
      height: 55.h,
      child: Center(
        child: Text(
          '${date.day}',
          style: GoogleFonts.roboto(
            color: Color(0xff71456F),
            fontWeight: FontWeight.w400,
            fontSize: 25.sp,
          ),
        ),
      ),
    );
  }
}
