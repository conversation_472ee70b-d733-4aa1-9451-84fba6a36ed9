import 'package:account_management/application/period_tracking_watcher_bloc/period_tracking_watcher_bloc.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:juno_plus/routing/app_pages.gr.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../custom_widgets/rounded_checkbox.dart';
import 'package:account_management/account_management.dart';
import '../../helpers.dart';

@RoutePage()
class PeriodTrackingEditPage extends StatelessWidget {
  const PeriodTrackingEditPage({super.key});

  @override
  Widget build(BuildContext context) {
    print('🔍 DEBUG: Building PeriodTrackingEditPage');

    try {
      print('🔍 DEBUG: Creating MultiBlocProvider');
      return MultiBlocProvider(
        providers: [
     
          BlocProvider<PeriodTrackingWatcherBloc>(create: (context) {
            print('🔍 DEBUG: Creating PeriodTrackingWatcherBloc');
            final bloc = getIt<PeriodTrackingWatcherBloc>();
            print('🔍 DEBUG: PeriodTrackingWatcherBloc created successfully');
            print('🔍 DEBUG: Adding watchYearStarted event');
            bloc.add(PeriodTrackingWatcherEvent.watchYearStarted(
                DateTime.now().year));
            print('🔍 DEBUG: watchYearStarted event added successfully');
            return bloc;
          }),
        ],
        child: PeriodTrackingEditScaffold(),
      );
    } catch (e) {
      print('❌ ERROR: Error in PeriodTrackingEditPage build: $e');
      return Scaffold(
        appBar: AppBar(title: Text('Error')),
        body: Center(
          child: Text('Error loading page: $e',
              style: TextStyle(color: Colors.red)),
        ),
      );
    }
  }
}

class PeriodTrackingEditScaffold extends StatefulWidget {
  const PeriodTrackingEditScaffold({super.key});

  @override
  State<PeriodTrackingEditScaffold> createState() =>
      _PeriodTrackingEditScaffoldState();
}

class _PeriodTrackingEditScaffoldState
    extends State<PeriodTrackingEditScaffold> {
  // Track locally selected dates for batch operations
  Set<DateTime> _localSelectedDates = {};
  Set<DateTime> _originalSelectedDates = {}; // Track original state
  bool _isInitialized = false;
  int _selectedYear = DateTime.now().year;

  // Get available years (current year and past 5 years)
  List<int> _getAvailableYears() {
    final currentYear = DateTime.now().year;
    List<int> years = [];

    for (int i = 0; i < 6; i++) {
      years.add(currentYear - i);
    }

    return years;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xffFAF2DF),
      appBar: AppBar(
        title: Text(
          'Edit Period Dates',
          style: GoogleFonts.roboto(
            fontSize: 20.sp,
            fontWeight: FontWeight.w500,
            color: Color(0xff71456F),
          ),
        ),
        backgroundColor: Color(0xffFAF2DF),
        elevation: 0,
        actions: [
          // Year dropdown
          Container(
            margin: EdgeInsets.only(right: 8.w),
            padding: EdgeInsets.symmetric(horizontal: 12.w),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: DropdownButton<int>(
              value: _selectedYear,
              underline: SizedBox(),
              items: _getAvailableYears().map((year) {
                return DropdownMenuItem<int>(
                  value: year,
                  child: Text(year.toString()),
                );
              }).toList(),
              onChanged: (newYear) {
                if (newYear != null && newYear != _selectedYear) {
                  setState(() {
                    _selectedYear = newYear;
                    _localSelectedDates
                        .clear(); // Clear local selections when changing year
                    _isInitialized = false; // Reset initialization flag
                  });
                  // Update the bloc to watch the new year
                  context.read<PeriodTrackingWatcherBloc>().add(
                        PeriodTrackingWatcherEvent.watchYearStarted(newYear),
                      );
                }
              },
            ),
          ),
          IconButton(
            icon: Icon(Icons.visibility, color: Color(0xff71456F)),
            onPressed: () {
              context.router.push(PeriodTrackingViewRoute());
            },
          ),
        ],
      ),
      body: BlocBuilder<PeriodTrackingWatcherBloc, PeriodTrackingWatcherState>(
        builder: (context, state) {
          print(
              '🔍 DEBUG: BlocBuilder called with state: ${state.runtimeType}');
          return state.maybeMap(
            loading: (_) {
              print('🔍 DEBUG: Showing loading state');
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16.h),
                    Text('Loading period data...'),
                  ],
                ),
              );
            },
            data: (dataState) {
              print(
                  '🔍 DEBUG: Showing data state with ${dataState.selectedDays.length} selected days');
              try {
                // Initialize local selected dates from existing data
                if (!_isInitialized) {
                  print('🔍 DEBUG: Initializing local selected dates');
                  _localSelectedDates = Set.from(dataState.selectedDays);
                  _originalSelectedDates =
                      Set.from(dataState.selectedDays); // Track original state
                  _isInitialized = true;
                  print('🔍 DEBUG: Initialization complete');
                }
                print('🔍 DEBUG: About to build calendar view');
                final result = _buildCalendarView(dataState);
                print('🔍 DEBUG: Calendar view built successfully');
                return result;
              } catch (e) {
                print('❌ ERROR: Error in data state handling: $e');
                return Center(
                  child: Text('Error: $e', style: TextStyle(color: Colors.red)),
                );
              }
            },
            orElse: () => Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.calendar_today, size: 64.w, color: Colors.grey),
                  SizedBox(height: 16.h),
                  Text(
                    'No period data found',
                    style: GoogleFonts.roboto(
                      fontSize: 18.sp,
                      color: Colors.grey,
                    ),
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    'Start by selecting some dates',
                    style: GoogleFonts.roboto(
                      fontSize: 14.sp,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildCalendarView(dynamic dataState) {
    print('🔍 DEBUG: _buildCalendarView called');
    try {
      return Column(
        children: [
          // Scrollable calendar months
          Expanded(
            child: _buildScrollableCalendar(dataState),
          ),
          // Done button
          _buildDoneButton(),
        ],
      );
    } catch (e) {
      print('❌ ERROR: Error in _buildCalendarView: $e');
      return Center(
        child: Text('Calendar View Error: $e',
            style: TextStyle(color: Colors.red)),
      );
    }
  }

  Widget _buildScrollableCalendar(dynamic dataState) {
    try {
      print('🔍 DEBUG: Building scrollable calendar for year: $_selectedYear');
      final months =
          List.generate(12, (index) => DateTime(_selectedYear, index + 1, 1));
      print('🔍 DEBUG: Generated ${months.length} months');

      return ListView.builder(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
        itemCount: months.length,
        itemBuilder: (context, index) {
          try {
            print(
                '🔍 DEBUG: Building month card for index: $index, month: ${months[index]}');
            return _buildMonthCard(months[index], dataState);
          } catch (e) {
            print('❌ ERROR: Error building month card at index $index: $e');
            return Container(
              height: 100.h,
              child: Center(
                child: Text('Month Error', style: TextStyle(color: Colors.red)),
              ),
            );
          }
        },
      );
    } catch (e) {
      print('❌ ERROR: Error building scrollable calendar: $e');
      return Container(
        height: 400.h,
        child: Center(
          child: Text('Calendar Error', style: TextStyle(color: Colors.red)),
        ),
      );
    }
  }

  Widget _buildMonthCard(DateTime month, dynamic dataState) {
    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.2),
            spreadRadius: 2,
            blurRadius: 5,
            offset: Offset(0, 3),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Month title
            Text(
              _getMonthYearText(month),
              style: GoogleFonts.roboto(
                fontSize: 20.sp,
                fontWeight: FontWeight.w600,
                color: Color(0xff71456F),
              ),
            ),
            SizedBox(height: 12.h),
            // Days of week header
            _buildDaysOfWeekHeader(),
            SizedBox(height: 8.h),
            // Calendar grid for this month
            _buildMonthCalendarGrid(month, dataState),
          ],
        ),
      ),
    );
  }

  Widget _buildDaysOfWeekHeader() {
    const daysOfWeek = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    return Row(
      children: daysOfWeek
          .map((day) => Expanded(
                child: Center(
                  child: Text(
                    day,
                    style: GoogleFonts.roboto(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w500,
                      color: Color(0xff71456F),
                    ),
                  ),
                ),
              ))
          .toList(),
    );
  }

  Widget _buildMonthCalendarGrid(DateTime month, dynamic dataState) {
    try {
      final daysInMonth = _getDaysInMonth(month);
      final firstDayOfMonth = DateTime(month.year, month.month, 1);
      final firstWeekday = firstDayOfMonth.weekday % 7; // Sunday = 0

      // Calculate how many rows we need
      final totalCells = firstWeekday + daysInMonth;
      final rows = (totalCells / 7).ceil();

      return GridView.builder(
        shrinkWrap: true,
        physics: NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 7,
          childAspectRatio: 0.8, // Adjusted for taller cells
          crossAxisSpacing: 4.w,
          mainAxisSpacing: 8.h,
        ),
        itemCount: rows * 7,
        itemBuilder: (context, index) {
          try {
            final dayIndex = index - firstWeekday;

            if (dayIndex < 0 || dayIndex >= daysInMonth) {
              // Empty cell for days outside current month
              return Container();
            }

            final date = DateTime(month.year, month.month, dayIndex + 1);

            // Inline the widget creation with proper styling and functionality
            final today = DateTime.now();
            final todayNormalized =
                DateTime(today.year, today.month, today.day);
            final dateNormalized = DateTime(date.year, date.month, date.day);
            final januaryFirst = DateTime(_selectedYear, 1, 1);

            // Check date properties
            bool isTodayDate = dateNormalized.isAtSameMomentAs(todayNormalized);
            bool isFutureDate = dateNormalized.isAfter(todayNormalized);
            bool isSelectableDate = dateNormalized
                    .isAfter(januaryFirst.subtract(Duration(days: 1))) &&
                !isFutureDate;

            // Check if this date is selected in local state
            bool isSelectedPeriodDate = _localSelectedDates.any((selectedDay) =>
                selectedDay.year == date.year &&
                selectedDay.month == date.month &&
                selectedDay.day == date.day);

            return Container(
              width: 50.w,
              height: 60.h,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Day number
                  Text(
                    '${date.day}',
                    style: GoogleFonts.roboto(
                      color: isFutureDate
                          ? Colors.grey.shade400
                          : Color(0xff71456F),
                      fontWeight: FontWeight.w500,
                      fontSize: 20.sp,
                    ),
                  ),
                  SizedBox(height: 4.h),
                  // Checkbox for selectable dates
                  if (isSelectableDate)
                    RoundedCheckbox(
                      isChecked: isSelectedPeriodDate,
                      size: 50.0,
                      checkedColor: Color(0xFFE91E63),
                      uncheckedColor: Colors.transparent,
                      onTap: () {
                        setState(() {
                          final normalizedDate =
                              DateTime(date.year, date.month, date.day);
                          if (isSelectedPeriodDate) {
                            _localSelectedDates.remove(normalizedDate);
                          } else {
                            _localSelectedDates.add(normalizedDate);
                          }
                        });
                      },
                    )
                  else
                    SizedBox(
                        height:
                            20.h), // Maintain spacing for non-selectable dates
                  // Today indicator
                  if (isTodayDate)
                    Container(
                      width: 30.w,
                      height: 2.h,
                      margin: EdgeInsets.only(top: 2.h),
                      decoration: BoxDecoration(
                        color: Color(0xFFE91E63),
                        borderRadius: BorderRadius.circular(1.r),
                      ),
                    ),
                ],
              ),
            );
          } catch (e) {
            print('❌ ERROR: Error in itemBuilder at index $index: $e');
            return Container(
              width: 50.w,
              height: 60.h,
              child: Center(
                child: Text('ERR',
                    style: TextStyle(color: Colors.red, fontSize: 12.sp)),
              ),
            );
          }
        },
      );
    } catch (e) {
      print('❌ ERROR: Error building month calendar grid: $e');
      return Container(
        height: 200.h,
        child: Center(
          child: Text('Calendar Error', style: TextStyle(color: Colors.red)),
        ),
      );
    }
  }

  Widget _buildDoneButton() {
  return ElevatedButton(
        onPressed: () {

        },
        style: ElevatedButton.styleFrom(
          backgroundColor: Color(0xFFE91E63),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(25.r),
          ),
          padding: EdgeInsets.symmetric(horizontal: 40.w, vertical: 12.h),
        ),
        child: Text(
          'Done',
          style: GoogleFonts.roboto(
            color: Colors.white,
            fontSize: 16.sp,
            fontWeight: FontWeight.w500,
          ),
        ),);
  }

  // Helper methods

  String _getMonthYearText(DateTime date) {
    const months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December'
    ];
    return '${months[date.month - 1]} ${date.year}';
  }

  int _getDaysInMonth(DateTime date) {
    return DateTime(date.year, date.month + 1, 0).day;
  }
}
