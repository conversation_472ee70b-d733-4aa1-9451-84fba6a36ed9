// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// AutoRouterGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:auto_route/auto_route.dart' as _i32;
import 'package:flutter/material.dart' as _i33;
import 'package:juno_plus/app.dart' as _i27;
import 'package:juno_plus/pages/connectivity/pair_page.dart' as _i15;
import 'package:juno_plus/pages/connectivity/troubleshooting_page.dart' as _i29;
import 'package:juno_plus/pages/dashboard/dashboard_page.dart' as _i1;
import 'package:juno_plus/pages/dashboard/extended_calendar.dart' as _i4;
import 'package:juno_plus/pages/dashboard/new_dashboard_page.dart' as _i12;
import 'package:juno_plus/pages/dashboard/period_tracking_edit_page.dart'
    as _i16;
import 'package:juno_plus/pages/dashboard/period_tracking_view_page.dart'
    as _i17;
import 'package:juno_plus/pages/dashboard/therapy_analytics_chart_page.dart'
    as _i28;
import 'package:juno_plus/pages/help_center/help_center_home.dart' as _i6;
import 'package:juno_plus/pages/home_page.dart' as _i7;
import 'package:juno_plus/pages/login/email_verification_page.dart' as _i3;
import 'package:juno_plus/pages/login/login_page.dart' as _i8;
import 'package:juno_plus/pages/login/reset_password_page.dart' as _i24;
import 'package:juno_plus/pages/login/sign_up_page.dart' as _i26;
import 'package:juno_plus/pages/login/welcome_page.dart' as _i31;
import 'package:juno_plus/pages/medications/medication.dart' as _i11;
import 'package:juno_plus/pages/medications/medication_cabinet.dart' as _i10;
import 'package:juno_plus/pages/medications/medication_cabinet_button.dart'
    as _i9;
import 'package:juno_plus/pages/notifications/notifications_page.dart' as _i13;
import 'package:juno_plus/pages/onboarding/get_started_page.dart' as _i5;
import 'package:juno_plus/pages/onboarding/onboarding_start.dart' as _i14;
import 'package:juno_plus/pages/product_showcase/product_showcase.dart' as _i18;
import 'package:juno_plus/pages/remote/remote_experiment_Page.dart' as _i21;
import 'package:juno_plus/pages/remote/remote_one_page.dart' as _i22;
import 'package:juno_plus/pages/remote/remote_two_page.dart' as _i23;
import 'package:juno_plus/pages/settings/device_settings_page.dart' as _i2;
import 'package:juno_plus/pages/settings/profile_page.dart' as _i19;
import 'package:juno_plus/pages/settings/profile_picture_page.dart' as _i20;
import 'package:juno_plus/pages/settings/settings_page.dart' as _i25;
import 'package:juno_plus/pages/virtual_remote/virtual_remote_page.dart'
    as _i30;

/// generated route for
/// [_i1.DashboardPage]
class DashboardRoute extends _i32.PageRouteInfo<void> {
  const DashboardRoute({List<_i32.PageRouteInfo>? children})
      : super(DashboardRoute.name, initialChildren: children);

  static const String name = 'DashboardRoute';

  static _i32.PageInfo page = _i32.PageInfo(
    name,
    builder: (data) {
      return _i1.DashboardPage();
    },
  );
}

/// generated route for
/// [_i2.DeviceSettingsPage]
class DeviceSettingsRoute extends _i32.PageRouteInfo<void> {
  const DeviceSettingsRoute({List<_i32.PageRouteInfo>? children})
      : super(DeviceSettingsRoute.name, initialChildren: children);

  static const String name = 'DeviceSettingsRoute';

  static _i32.PageInfo page = _i32.PageInfo(
    name,
    builder: (data) {
      return const _i2.DeviceSettingsPage();
    },
  );
}

/// generated route for
/// [_i3.EmailVerificationPage]
class EmailVerificationRoute extends _i32.PageRouteInfo<void> {
  const EmailVerificationRoute({List<_i32.PageRouteInfo>? children})
      : super(EmailVerificationRoute.name, initialChildren: children);

  static const String name = 'EmailVerificationRoute';

  static _i32.PageInfo page = _i32.PageInfo(
    name,
    builder: (data) {
      return const _i3.EmailVerificationPage();
    },
  );
}

/// generated route for
/// [_i4.ExtendedCalenderPage]
class ExtendedCalenderRoute extends _i32.PageRouteInfo<void> {
  const ExtendedCalenderRoute({List<_i32.PageRouteInfo>? children})
      : super(ExtendedCalenderRoute.name, initialChildren: children);

  static const String name = 'ExtendedCalenderRoute';

  static _i32.PageInfo page = _i32.PageInfo(
    name,
    builder: (data) {
      return const _i4.ExtendedCalenderPage();
    },
  );
}

/// generated route for
/// [_i5.GetStartedPage]
class GetStartedRoute extends _i32.PageRouteInfo<void> {
  const GetStartedRoute({List<_i32.PageRouteInfo>? children})
      : super(GetStartedRoute.name, initialChildren: children);

  static const String name = 'GetStartedRoute';

  static _i32.PageInfo page = _i32.PageInfo(
    name,
    builder: (data) {
      return _i5.GetStartedPage();
    },
  );
}

/// generated route for
/// [_i6.HelpCenterHomePage]
class HelpCenterHomeRoute extends _i32.PageRouteInfo<void> {
  const HelpCenterHomeRoute({List<_i32.PageRouteInfo>? children})
      : super(HelpCenterHomeRoute.name, initialChildren: children);

  static const String name = 'HelpCenterHomeRoute';

  static _i32.PageInfo page = _i32.PageInfo(
    name,
    builder: (data) {
      return const _i6.HelpCenterHomePage();
    },
  );
}

/// generated route for
/// [_i7.HomePage]
class HomeRoute extends _i32.PageRouteInfo<void> {
  const HomeRoute({List<_i32.PageRouteInfo>? children})
      : super(HomeRoute.name, initialChildren: children);

  static const String name = 'HomeRoute';

  static _i32.PageInfo page = _i32.PageInfo(
    name,
    builder: (data) {
      return const _i7.HomePage();
    },
  );
}

/// generated route for
/// [_i8.LoginPage]
class LoginRoute extends _i32.PageRouteInfo<void> {
  const LoginRoute({List<_i32.PageRouteInfo>? children})
      : super(LoginRoute.name, initialChildren: children);

  static const String name = 'LoginRoute';

  static _i32.PageInfo page = _i32.PageInfo(
    name,
    builder: (data) {
      return const _i8.LoginPage();
    },
  );
}

/// generated route for
/// [_i9.MedicationCabinetButton]
class MedicationCabinetButton extends _i32.PageRouteInfo<void> {
  const MedicationCabinetButton({List<_i32.PageRouteInfo>? children})
      : super(MedicationCabinetButton.name, initialChildren: children);

  static const String name = 'MedicationCabinetButton';

  static _i32.PageInfo page = _i32.PageInfo(
    name,
    builder: (data) {
      return const _i9.MedicationCabinetButton();
    },
  );
}

/// generated route for
/// [_i10.MedicationCabinetPage]
class MedicationCabinetRoute extends _i32.PageRouteInfo<void> {
  const MedicationCabinetRoute({List<_i32.PageRouteInfo>? children})
      : super(MedicationCabinetRoute.name, initialChildren: children);

  static const String name = 'MedicationCabinetRoute';

  static _i32.PageInfo page = _i32.PageInfo(
    name,
    builder: (data) {
      return const _i10.MedicationCabinetPage();
    },
  );
}

/// generated route for
/// [_i11.MedicationPage]
class MedicationRoute extends _i32.PageRouteInfo<void> {
  const MedicationRoute({List<_i32.PageRouteInfo>? children})
      : super(MedicationRoute.name, initialChildren: children);

  static const String name = 'MedicationRoute';

  static _i32.PageInfo page = _i32.PageInfo(
    name,
    builder: (data) {
      return const _i11.MedicationPage();
    },
  );
}

/// generated route for
/// [_i12.NewDashboardPage]
class NewDashboardRoute extends _i32.PageRouteInfo<void> {
  const NewDashboardRoute({List<_i32.PageRouteInfo>? children})
      : super(NewDashboardRoute.name, initialChildren: children);

  static const String name = 'NewDashboardRoute';

  static _i32.PageInfo page = _i32.PageInfo(
    name,
    builder: (data) {
      return _i12.NewDashboardPage();
    },
  );
}

/// generated route for
/// [_i13.NotificationsPage]
class NotificationsRoute extends _i32.PageRouteInfo<void> {
  const NotificationsRoute({List<_i32.PageRouteInfo>? children})
      : super(NotificationsRoute.name, initialChildren: children);

  static const String name = 'NotificationsRoute';

  static _i32.PageInfo page = _i32.PageInfo(
    name,
    builder: (data) {
      return const _i13.NotificationsPage();
    },
  );
}

/// generated route for
/// [_i14.OnboardingStartScreen]
class OnboardingStartScreen extends _i32.PageRouteInfo<void> {
  const OnboardingStartScreen({List<_i32.PageRouteInfo>? children})
      : super(OnboardingStartScreen.name, initialChildren: children);

  static const String name = 'OnboardingStartScreen';

  static _i32.PageInfo page = _i32.PageInfo(
    name,
    builder: (data) {
      return const _i14.OnboardingStartScreen();
    },
  );
}

/// generated route for
/// [_i15.PairDevicePage]
class PairDeviceRoute extends _i32.PageRouteInfo<void> {
  const PairDeviceRoute({List<_i32.PageRouteInfo>? children})
      : super(PairDeviceRoute.name, initialChildren: children);

  static const String name = 'PairDeviceRoute';

  static _i32.PageInfo page = _i32.PageInfo(
    name,
    builder: (data) {
      return const _i15.PairDevicePage();
    },
  );
}

/// generated route for
/// [_i16.PeriodTrackingEditPage]
class PeriodTrackingEditRoute extends _i32.PageRouteInfo<void> {
  const PeriodTrackingEditRoute({List<_i32.PageRouteInfo>? children})
      : super(PeriodTrackingEditRoute.name, initialChildren: children);

  static const String name = 'PeriodTrackingEditRoute';

  static _i32.PageInfo page = _i32.PageInfo(
    name,
    builder: (data) {
      return const _i16.PeriodTrackingEditPage();
    },
  );
}

/// generated route for
/// [_i17.PeriodTrackingViewPage]
class PeriodTrackingViewRoute extends _i32.PageRouteInfo<void> {
  const PeriodTrackingViewRoute({List<_i32.PageRouteInfo>? children})
      : super(PeriodTrackingViewRoute.name, initialChildren: children);

  static const String name = 'PeriodTrackingViewRoute';

  static _i32.PageInfo page = _i32.PageInfo(
    name,
    builder: (data) {
      return const _i17.PeriodTrackingViewPage();
    },
  );
}

/// generated route for
/// [_i18.ProductShowcasePage]
class ProductShowcaseRoute extends _i32.PageRouteInfo<void> {
  const ProductShowcaseRoute({List<_i32.PageRouteInfo>? children})
      : super(ProductShowcaseRoute.name, initialChildren: children);

  static const String name = 'ProductShowcaseRoute';

  static _i32.PageInfo page = _i32.PageInfo(
    name,
    builder: (data) {
      return const _i18.ProductShowcasePage();
    },
  );
}

/// generated route for
/// [_i19.ProfilePage]
class ProfileRoute extends _i32.PageRouteInfo<ProfileRouteArgs> {
  ProfileRoute({_i33.Key? key, List<_i32.PageRouteInfo>? children})
      : super(
          ProfileRoute.name,
          args: ProfileRouteArgs(key: key),
          initialChildren: children,
        );

  static const String name = 'ProfileRoute';

  static _i32.PageInfo page = _i32.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<ProfileRouteArgs>(
        orElse: () => const ProfileRouteArgs(),
      );
      return _i19.ProfilePage(key: args.key);
    },
  );
}

class ProfileRouteArgs {
  const ProfileRouteArgs({this.key});

  final _i33.Key? key;

  @override
  String toString() {
    return 'ProfileRouteArgs{key: $key}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! ProfileRouteArgs) return false;
    return key == other.key;
  }

  @override
  int get hashCode => key.hashCode;
}

/// generated route for
/// [_i20.ProfilePicturePage]
class ProfilePictureRoute extends _i32.PageRouteInfo<void> {
  const ProfilePictureRoute({List<_i32.PageRouteInfo>? children})
      : super(ProfilePictureRoute.name, initialChildren: children);

  static const String name = 'ProfilePictureRoute';

  static _i32.PageInfo page = _i32.PageInfo(
    name,
    builder: (data) {
      return _i20.ProfilePicturePage();
    },
  );
}

/// generated route for
/// [_i21.RemoteExperimentPage]
class RemoteExperimentRoute extends _i32.PageRouteInfo<void> {
  const RemoteExperimentRoute({List<_i32.PageRouteInfo>? children})
      : super(RemoteExperimentRoute.name, initialChildren: children);

  static const String name = 'RemoteExperimentRoute';

  static _i32.PageInfo page = _i32.PageInfo(
    name,
    builder: (data) {
      return _i21.RemoteExperimentPage();
    },
  );
}

/// generated route for
/// [_i22.RemoteOnePage]
class RemoteOneRoute extends _i32.PageRouteInfo<void> {
  const RemoteOneRoute({List<_i32.PageRouteInfo>? children})
      : super(RemoteOneRoute.name, initialChildren: children);

  static const String name = 'RemoteOneRoute';

  static _i32.PageInfo page = _i32.PageInfo(
    name,
    builder: (data) {
      return _i22.RemoteOnePage();
    },
  );
}

/// generated route for
/// [_i23.RemoteTwoPage]
class RemoteTwoRoute extends _i32.PageRouteInfo<void> {
  const RemoteTwoRoute({List<_i32.PageRouteInfo>? children})
      : super(RemoteTwoRoute.name, initialChildren: children);

  static const String name = 'RemoteTwoRoute';

  static _i32.PageInfo page = _i32.PageInfo(
    name,
    builder: (data) {
      return _i23.RemoteTwoPage();
    },
  );
}

/// generated route for
/// [_i24.ResetPasswordPage]
class ResetPasswordRoute extends _i32.PageRouteInfo<void> {
  const ResetPasswordRoute({List<_i32.PageRouteInfo>? children})
      : super(ResetPasswordRoute.name, initialChildren: children);

  static const String name = 'ResetPasswordRoute';

  static _i32.PageInfo page = _i32.PageInfo(
    name,
    builder: (data) {
      return const _i24.ResetPasswordPage();
    },
  );
}

/// generated route for
/// [_i25.SettingsPage]
class SettingsRoute extends _i32.PageRouteInfo<void> {
  const SettingsRoute({List<_i32.PageRouteInfo>? children})
      : super(SettingsRoute.name, initialChildren: children);

  static const String name = 'SettingsRoute';

  static _i32.PageInfo page = _i32.PageInfo(
    name,
    builder: (data) {
      return const _i25.SettingsPage();
    },
  );
}

/// generated route for
/// [_i26.SignUpPage]
class SignUpRoute extends _i32.PageRouteInfo<void> {
  const SignUpRoute({List<_i32.PageRouteInfo>? children})
      : super(SignUpRoute.name, initialChildren: children);

  static const String name = 'SignUpRoute';

  static _i32.PageInfo page = _i32.PageInfo(
    name,
    builder: (data) {
      return const _i26.SignUpPage();
    },
  );
}

/// generated route for
/// [_i27.SplashPage]
class SplashRoute extends _i32.PageRouteInfo<void> {
  const SplashRoute({List<_i32.PageRouteInfo>? children})
      : super(SplashRoute.name, initialChildren: children);

  static const String name = 'SplashRoute';

  static _i32.PageInfo page = _i32.PageInfo(
    name,
    builder: (data) {
      return const _i27.SplashPage();
    },
  );
}

/// generated route for
/// [_i28.TherapyAnalyticsChartPage]
class TherapyAnalyticsChartRoute extends _i32.PageRouteInfo<void> {
  const TherapyAnalyticsChartRoute({List<_i32.PageRouteInfo>? children})
      : super(TherapyAnalyticsChartRoute.name, initialChildren: children);

  static const String name = 'TherapyAnalyticsChartRoute';

  static _i32.PageInfo page = _i32.PageInfo(
    name,
    builder: (data) {
      return _i28.TherapyAnalyticsChartPage();
    },
  );
}

/// generated route for
/// [_i29.TroubleshootingPage]
class TroubleshootingRoute extends _i32.PageRouteInfo<void> {
  const TroubleshootingRoute({List<_i32.PageRouteInfo>? children})
      : super(TroubleshootingRoute.name, initialChildren: children);

  static const String name = 'TroubleshootingRoute';

  static _i32.PageInfo page = _i32.PageInfo(
    name,
    builder: (data) {
      return _i29.TroubleshootingPage();
    },
  );
}

/// generated route for
/// [_i30.VirtualRemotePage]
class VirtualRemoteRoute extends _i32.PageRouteInfo<void> {
  const VirtualRemoteRoute({List<_i32.PageRouteInfo>? children})
      : super(VirtualRemoteRoute.name, initialChildren: children);

  static const String name = 'VirtualRemoteRoute';

  static _i32.PageInfo page = _i32.PageInfo(
    name,
    builder: (data) {
      return _i30.VirtualRemotePage();
    },
  );
}

/// generated route for
/// [_i31.WelcomePage]
class WelcomeRoute extends _i32.PageRouteInfo<void> {
  const WelcomeRoute({List<_i32.PageRouteInfo>? children})
      : super(WelcomeRoute.name, initialChildren: children);

  static const String name = 'WelcomeRoute';

  static _i32.PageInfo page = _i32.PageInfo(
    name,
    builder: (data) {
      return const _i31.WelcomePage();
    },
  );
}
